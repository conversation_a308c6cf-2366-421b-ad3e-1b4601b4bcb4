package com.jcloud.common.mapper;

import com.jcloud.common.entity.SysRole;
import com.jcloud.common.entity.SysUserRole;
import com.mybatisflex.core.BaseMapper;
import lombok.Data;
import org.apache.ibatis.annotations.*;

import java.util.List;



/**
 * 用户角色关联Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} AND r.deleted = 0")
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId} AND tenant_id = #{tenantId}")
    int deleteByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM sys_user_role WHERE role_id = #{roleId} AND tenant_id = #{tenantId}")
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);

    /**
     * 根据角色编码查询用户ID列表
     *
     * @param roleCode 角色编码
     * @param tenantId 租户ID
     * @return 用户ID列表
     */
    @Select("SELECT ur.user_id FROM sys_user_role ur " +
            "INNER JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE r.role_code = #{roleCode} AND ur.tenant_id = #{tenantId} AND r.deleted = 0")
    List<Long> selectUserIdsByRoleCode(@Param("roleCode") String roleCode, @Param("tenantId") Long tenantId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 删除行数
     */
    @Delete("DELETE FROM sys_user_role WHERE role_id = #{roleId} AND tenant_id = #{tenantId}")
    int deleteByRoleId(@Param("roleId") Long roleId, @Param("tenantId") Long tenantId);

    /**
     * 检查用户是否拥有指定角色
     *
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @param tenantId 租户ID
     * @return 是否拥有角色
     */
    @Select("SELECT COUNT(1) > 0 FROM sys_user_role ur " +
            "INNER JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE ur.user_id = #{userId} AND r.role_code = #{roleCode} " +
            "AND ur.tenant_id = #{tenantId} AND r.deleted = 0")
    boolean hasRole(@Param("userId") Long userId,
                   @Param("roleCode") String roleCode,
                   @Param("tenantId") Long tenantId);

    /**
     * 根据用户ID查询角色编码列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色编码列表
     */
    @Select("SELECT r.role_code FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND ur.tenant_id = #{tenantId} " +
            "AND r.status = 1 AND r.deleted = 0")
    List<String> selectRoleCodesByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    /**
     * 批量查询用户角色信息
     *
     * @param userIds 用户ID列表
     * @param tenantId 租户ID
     * @return 用户角色映射结果
     */
    @Select("<script>" +
            "SELECT ur.user_id, r.id as role_id, r.role_name, r.role_code, r.status, " +
            "r.sort_order, r.remark, r.create_time, r.update_time, r.create_by, r.update_by, " +
            "r.deleted, r.tenant_id, r.version " +
            "FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.tenant_id = #{tenantId} AND r.deleted = 0 " +
            "<if test='userIds != null and userIds.size() > 0'>" +
            "AND ur.user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</if>" +
            "ORDER BY ur.user_id, r.sort_order" +
            "</script>")
    @Results({
        @Result(column = "user_id", property = "userId"),
        @Result(column = "role_id", property = "id"),
        @Result(column = "role_name", property = "roleName"),
        @Result(column = "role_code", property = "roleCode"),
        @Result(column = "status", property = "status"),
        @Result(column = "sort_order", property = "sortOrder"),
        @Result(column = "remark", property = "remark"),
        @Result(column = "create_time", property = "createTime"),
        @Result(column = "update_time", property = "updateTime"),
        @Result(column = "create_by", property = "createBy"),
        @Result(column = "update_by", property = "updateBy"),
        @Result(column = "deleted", property = "deleted"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "version", property = "version")
    })
    List<UserRoleResult> selectRolesByUserIds(@Param("userIds") List<Long> userIds, @Param("tenantId") Long tenantId);

    /**
     * 用户角色查询结果类
     */
    @Data
    class UserRoleResult {
        private Long userId;
        private Long id;
        private String roleName;
        private String roleCode;
        private Integer status;
        private Integer sortOrder;
        private String remark;
        private Long createTime;
        private Long updateTime;
        private Long createBy;
        private Long updateBy;
        private Integer deleted;
        private Long tenantId;
        private Integer version;
    }

}
