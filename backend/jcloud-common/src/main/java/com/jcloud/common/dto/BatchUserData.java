package com.jcloud.common.dto;

import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 批量用户数据准备类
 * 用于封装批量用户操作所需的所有数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchUserData {
    
    /**
     * 原始VIM用户数据
     */
    private List<VimUser> vimUsers;
    
    /**
     * 转换后的系统用户数据
     */
    private List<SysUser> sysUsers;
    
    /**
     * VIM用户与系统用户的映射关系（通过索引）
     */
    private Map<Integer, Integer> userIndexMapping;
    
    /**
     * 已存在的用户（手机号 -> 用户对象）
     */
    private Map<String, SysUser> existingUsers;
    
    /**
     * 需要创建的新用户
     */
    private List<SysUser> newUsers;
    
    /**
     * 需要创建的新用户对应的VIM用户
     */
    private List<VimUser> newVimUsers;
    
    /**
     * 用户角色分配数据（用户ID -> 角色ID列表）
     */
    private Map<Long, List<Long>> userRoleAssignments;
    
    /**
     * 用户密码数据（用户ID -> 密码）
     */
    private Map<Long, String> userPasswords;
    
    /**
     * 代理用户数据（需要创建部门的用户）
     */
    private List<VimUser> agentUsers;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 批次大小配置
     */
    @Builder.Default
    private int batchSize = 100;
    
    /**
     * 获取需要处理的用户总数
     */
    public int getTotalUserCount() {
        return vimUsers != null ? vimUsers.size() : 0;
    }
    
    /**
     * 获取新用户数量
     */
    public int getNewUserCount() {
        return newUsers != null ? newUsers.size() : 0;
    }
    
    /**
     * 获取已存在用户数量
     */
    public int getExistingUserCount() {
        return existingUsers != null ? existingUsers.size() : 0;
    }
    
    /**
     * 检查数据是否已准备完成
     */
    public boolean isDataPrepared() {
        return sysUsers != null && !sysUsers.isEmpty() &&
               newUsers != null && newVimUsers != null &&
               newUsers.size() == newVimUsers.size();
    }
}
