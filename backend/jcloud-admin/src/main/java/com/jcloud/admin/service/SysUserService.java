package com.jcloud.admin.service;

import com.jcloud.admin.vo.SysUserVO;
import com.jcloud.common.dto.UserCreateRequest;
import com.jcloud.common.dto.UserQueryRequest;
import com.jcloud.common.dto.UserUpdateRequest;
import com.jcloud.common.dto.BatchOperationResult;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import com.jcloud.common.enums.BatchTransactionMode;
import com.jcloud.admin.vo.SysUserDetailVO;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysUserService extends BaseService<SysUser> {
    
    /**
     * 分页查询用户列表
     * @param queryRequest 查询条件
     * @return 用户分页列表
     */
    PageResult<SysUser> pageUsers(UserQueryRequest queryRequest);

    /**
     * 分页查询用户列表（脱敏版本，用于前端显示）
     * @param queryRequest 查询条件
     * @return 脱敏的用户分页列表
     */
    PageResult<SysUserVO> pageUsersForDisplay(UserQueryRequest queryRequest);
    
    /**
     * 创建用户
     * @param createRequest 创建请求
     * @return 是否成功
     */
    boolean createUser(UserCreateRequest createRequest);
    
    /**
     * 更新用户
     * @param updateRequest 更新请求
     * @return 是否成功
     */
    boolean updateUser(UserUpdateRequest updateRequest);

    /**
     * 获取用户详情（包含部门和角色信息，脱敏版本用于查看）
     * @param userId 用户ID
     * @return 用户详情
     */
    SysUserDetailVO getUserDetailById(Long userId);

    /**
     * 获取用户详情（包含部门和角色信息，原始版本用于编辑）
     * @param userId 用户ID
     * @return 用户详情
     */
    SysUserDetailVO getUserDetailForEdit(Long userId);
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    SysUser getUserByEmail(String email);
    
    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    SysUser getUserByPhone(String phone);
    
    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(Long userId, String newPassword);
    
    /**
     * 修改用户密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 启用/禁用用户
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateUserStatus(Long userId, Integer status);
    
    /**
     * 批量启用/禁用用户
     * 
     * @param userIds 用户ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean updateUserStatusBatch(List<Long> userIds, Integer status);
    
    /**
     * 分配用户角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean assignRoles(Long userId, List<Long> roleIds);
    
    /**
     * 获取用户角色列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getUserRoleIds(Long userId);
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeUserId);
    
    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeUserId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeUserId);

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> getUserPermissions(Long userId);

    /**
     * 获取用户权限列表（优化版本，避免重复查询）
     *
     * @param user 用户对象
     * @return 权限列表
     */
    Set<String> getUserPermissions(SysUser user);

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    Set<String> getUserRoles(Long userId);

    /**
     * 获取用户角色列表（优化版本，避免重复查询）
     *
     * @param user 用户对象
     * @return 角色列表
     */
    Set<String> getUserRoles(SysUser user);

    /**
     * 批量保存用户并分配角色
     *
     * @param vimUsers 原始用户数据
     * @param mode 事务模式
     * @return 批量操作结果
     */
    BatchOperationResult<SysUser> batchSaveUsersWithRoles(List<VimUser> vimUsers, BatchTransactionMode mode);



    /**
     * 根据用户ID列表批量查询用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    List<SysUser> listByIds(List<Long> userIds);

    /**
     * 清理指定用户名的缓存
     *
     * @param username 用户名
     */
    void clearUserCacheByUsername(String username);

    /**
     * 直接从数据库查询用户（不使用缓存）
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsernameFromDatabase(String username);

    /**
     * 查询待同步的用户（从vim_user表）
     *
     * @return 待同步用户列表
     */
    List<VimUser> queryUsersForSync();

    /**
     * 批量查询现有用户（根据手机号）
     *
     * @param phones 手机号列表
     * @param tenantId 租户ID
     * @return 现有用户映射（手机号 -> 用户对象）
     */
    Map<String, SysUser> batchQueryExistingUsersByPhone(List<String> phones, Long tenantId);

    /**
     * 为代理用户创建专属部门并分配用户
     *
     * @param agentUser 代理用户信息
     * @param agentUserId 代理用户ID
     */
    void createAgentDeptAndAssignUsers(VimUser agentUser, Long agentUserId);

    /**
     * 清理用户相关缓存
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     */
    void clearUserRelatedCaches(Long userId, Long tenantId);
}
