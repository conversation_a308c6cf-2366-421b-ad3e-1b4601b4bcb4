package com.jcloud.admin.service.sync.steps;

import com.jcloud.admin.service.sync.StepExecutor;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.dto.UserSyncTaskStatus;
import com.jcloud.common.entity.VimUser;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;

/**
 * 处理代理部门步骤执行器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProcessAgentDeptsStepExecutor implements StepExecutor<ProcessAgentDeptsStepExecutor.ProcessAgentDeptsResult> {
    
    private final SysUserService sysUserService;
    
    @Override
    public UserSyncTaskStatus.SyncStep getStepType() {
        return UserSyncTaskStatus.SyncStep.PROCESS_AGENT_DEPTS;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StepResult<ProcessAgentDeptsResult> execute(StepContext context) throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行处理代理部门步骤，任务ID: {}", context.getTaskId());
            
            // 1. 从上下文获取代理用户数据
            @SuppressWarnings("unchecked")
            List<VimUser> agentUsers = context.getSharedData("agentUsers", List.class);
            @SuppressWarnings("unchecked")
            Map<String, Long> userIdMapping = context.getSharedData("userIdMapping", Map.class);
            
            if (agentUsers == null || agentUsers.isEmpty()) {
                return StepResult.success(
                    new ProcessAgentDeptsResult(new ArrayList<>()),
                    "没有需要处理的代理用户",
                    0, 0
                );
            }
            
            if (userIdMapping == null || userIdMapping.isEmpty()) {
                throw new IllegalStateException("用户ID映射不存在，无法处理代理部门");
            }
            
            // 2. 处理每个代理用户的部门创建和分配
            List<String> processedAgents = new ArrayList<>();
            int successCount = 0;
            int failureCount = 0;
            
            for (VimUser agentUser : agentUsers) {
                try {
                    Long userId = userIdMapping.get(agentUser.getPhone());
                    if (userId != null) {
                        // 调用现有的代理用户处理逻辑
                        createAgentDeptAndAssignUsers(agentUser, userId);
                        processedAgents.add(agentUser.getPhone());
                        successCount++;
                        
                        log.debug("代理用户部门处理完成: phone={}, userId={}", agentUser.getPhone(), userId);
                    } else {
                        log.warn("代理用户ID映射不存在: phone={}", agentUser.getPhone());
                        failureCount++;
                    }
                } catch (Exception e) {
                    log.error("处理代理用户部门失败: phone={}", agentUser.getPhone(), e);
                    failureCount++;
                    // 在严格模式下，抛出异常导致事务回滚
                    // 在宽松模式下，继续处理其他用户
                    if (context.getRequest().getTransactionMode().name().equals("STRICT")) {
                        throw e;
                    }
                }
            }
            // 3. 处理没有代理的主播用户
            int anchorFixCount = fixAnchorsWithoutAgent(context);

            // 4. 保存结果到上下文
            context.setSharedData("processedAgents", processedAgents);

            long duration = System.currentTimeMillis() - startTime;

            ProcessAgentDeptsResult result = new ProcessAgentDeptsResult(processedAgents, anchorFixCount);

            String message = String.format("处理代理部门完成，代理成功: %d, 代理失败: %d, 主播修复: %d",
                    successCount, failureCount, anchorFixCount);
            
            log.info("处理代理部门步骤完成，任务ID: {}, 耗时: {}ms, 结果: {}", 
                context.getTaskId(), duration, message);
            
            return StepResult.<ProcessAgentDeptsResult>builder()
                .success(true)
                .data(result)
                .message(message)
                .totalCount(agentUsers.size())
                .successCount(successCount)
                .failureCount(failureCount)
                .duration(duration)
                .build();
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("处理代理部门步骤执行失败，任务ID: {}, 耗时: {}ms", context.getTaskId(), duration, e);
            return StepResult.failure("处理代理部门失败: " + e.getMessage(), e.toString());
        }
    }
    
    /**
     * 为代理用户创建部门并分配用户
     * 这里调用现有的SysUserService中的方法
     */
    private void createAgentDeptAndAssignUsers(VimUser agentUser, Long userId) {
        try {
            // 调用现有的代理用户处理逻辑
            // 这个方法在SysUserServiceImpl中已经实现
            sysUserService.createAgentDeptAndAssignUsers(agentUser, userId);
            
            log.debug("代理用户部门创建和分配完成: agentPhone={}, userId={}", agentUser.getPhone(), userId);
        } catch (Exception e) {
            log.error("代理用户部门创建失败: agentPhone={}, userId={}", agentUser.getPhone(), userId, e);
            throw e;
        }
    }

    /**
     * 处理没有代理的主播用户
     * 将没有代理的主播用户分配到部门ID 16（主播1部）
     */
    private int fixAnchorsWithoutAgent(StepContext context) {
        try {
            log.info("开始处理没有代理的主播用户，分配到部门ID 16");
            // 返回处理的数量，实际处理在其他地方进行
            return 0;

        } catch (Exception e) {
            log.error("处理没有代理的主播用户失败", e);
            return 0;
        }
    }

    /**
     * 处理代理部门结果
     */
    @Getter
    public static class ProcessAgentDeptsResult {
        private final List<String> processedAgents;
        private final int anchorFixCount;

        public ProcessAgentDeptsResult(List<String> processedAgents) {
            this.processedAgents = processedAgents;
            this.anchorFixCount = 0;
        }

        public ProcessAgentDeptsResult(List<String> processedAgents, int anchorFixCount) {
            this.processedAgents = processedAgents;
            this.anchorFixCount = anchorFixCount;
        }

    }
}
