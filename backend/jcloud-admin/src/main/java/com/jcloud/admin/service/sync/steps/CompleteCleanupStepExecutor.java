package com.jcloud.admin.service.sync.steps;

import com.jcloud.admin.service.sync.StepExecutor;
import com.jcloud.admin.service.sync.StepContext;
import com.jcloud.admin.service.sync.StepResult;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.dto.UserSyncExecuteRequest;
import com.jcloud.common.dto.UserSyncTaskStatus;
import com.jcloud.common.dto.BatchOperationResult;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.entity.VimUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 完成清理步骤执行器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CompleteCleanupStepExecutor implements StepExecutor<CompleteCleanupStepExecutor.CompleteCleanupResult> {
    
    private final SysUserService sysUserService;
    
    @Override
    public UserSyncTaskStatus.SyncStep getStepType() {
        return UserSyncTaskStatus.SyncStep.COMPLETE_CLEANUP;
    }
    
    @Override
    public StepResult<CompleteCleanupResult> execute(StepContext context) throws Exception {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行完成清理步骤，任务ID: {}", context.getTaskId());
            
            // 1. 从上下文收集所有执行结果
            @SuppressWarnings("unchecked")
            List<SysUser> savedUsers = context.getSharedData("savedUsers", List.class);
            @SuppressWarnings("unchecked")
            List<VimUser> usersToSync = context.getSharedData("usersToSync", List.class);
            @SuppressWarnings("unchecked")
            List<VimUser> newVimUsers = context.getSharedData("newVimUsers", List.class);
            @SuppressWarnings("unchecked")
            Map<String, Object> existingUsers = context.getSharedData("existingUsers", Map.class);
            @SuppressWarnings("unchecked")
            List<String> processedAgents = context.getSharedData("processedAgents", List.class);

            // 获取用户实际选择的数量
            UserSyncExecuteRequest request = context.getRequest();
            int selectedUserCount = 0;
            if (request.getSelectedUserPhones() != null && !request.getSelectedUserPhones().isEmpty()) {
                selectedUserCount = request.getSelectedUserPhones().size();
            } else if (usersToSync != null) {
                selectedUserCount = usersToSync.size();
            }

            // 2. 构建最终的同步结果
            BatchOperationResult<SysUser> syncResult = buildSyncResult(savedUsers, selectedUserCount, existingUsers);
            
            // 3. 清理用户相关缓存
            clearUserRelatedCaches(savedUsers);
            
            // 4. 清理步骤间的临时数据
            cleanupTemporaryData(context);
            
            // 5. 生成同步报告
            String syncReport = generateSyncReport(syncResult, processedAgents);
            
            long duration = System.currentTimeMillis() - startTime;
            
            CompleteCleanupResult result = new CompleteCleanupResult(syncResult, syncReport);
            
            String message = String.format("同步完成清理完成，总计处理: %d 个用户", syncResult.getTotalCount());
            
            log.info("完成清理步骤完成，任务ID: {}, 耗时: {}ms, 结果: {}", 
                context.getTaskId(), duration, message);
            
            // 将最终结果保存到上下文，供任务状态更新使用
            context.setSharedData("finalSyncResult", syncResult);
            
            return StepResult.<CompleteCleanupResult>builder()
                .success(true)
                .data(result)
                .message(message)
                .totalCount(syncResult.getTotalCount())
                .successCount(syncResult.getSuccessCount())
                .failureCount(syncResult.getFailureCount())
                .skipCount(syncResult.getSkipCount())
                .duration(duration)
                .build();
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("完成清理步骤执行失败，任务ID: {}, 耗时: {}ms", context.getTaskId(), duration, e);
            return StepResult.failure("完成清理失败: " + e.getMessage(), e.toString());
        }
    }
    
    /**
     * 构建同步结果
     * 修复：确保统计数据的准确性，避免前端显示nan
     */
    private BatchOperationResult<SysUser> buildSyncResult(List<SysUser> savedUsers, int selectedUserCount,
                                                         Map<String, Object> existingUsers) {
        // 修复：使用正确的统计数据
        // selectedUserCount 是用户实际选择的用户数量（来自前端选择）
        // savedUsers 是实际成功保存的用户
        // existingUsers 是已存在的用户（跳过的）

        int successCount = savedUsers != null ? savedUsers.size() : 0;
        int skipCount = existingUsers != null ? existingUsers.size() : 0;

        // 修复：总数应该是用户选择的用户数量
        int totalCount = selectedUserCount > 0 ? selectedUserCount : (successCount + skipCount);
        int failureCount = Math.max(0, totalCount - successCount - skipCount);

        log.info("构建同步结果: 用户选择={}, 总数={}, 成功={}, 失败={}, 跳过={}",
                selectedUserCount, totalCount, successCount, failureCount, skipCount);

        BatchOperationResult<SysUser> result = BatchOperationResult.<SysUser>builder()
            .totalCount(totalCount)
            .successCount(successCount)
            .failureCount(failureCount)
            .skipCount(skipCount)
            .build();

        // 添加成功的用户到结果中
        if (savedUsers != null) {
            savedUsers.forEach(result::addSuccessItem);
        }

        return result;
    }
    
    /**
     * 清理用户相关缓存
     */
    private void clearUserRelatedCaches(List<SysUser> savedUsers) {
        if (savedUsers == null || savedUsers.isEmpty()) {
            return;
        }
        
        try {
            for (SysUser user : savedUsers) {
                sysUserService.clearUserRelatedCaches(user.getId(), user.getTenantId());
            }
            log.debug("清理用户相关缓存完成，用户数量: {}", savedUsers.size());
        } catch (Exception e) {
            log.warn("清理用户相关缓存失败", e);
            // 缓存清理失败不影响主流程
        }
    }
    
    /**
     * 清理步骤间的临时数据
     */
    private void cleanupTemporaryData(StepContext context) {
        try {
            // 清理不再需要的临时数据，保留最终结果
            context.removeSharedData("usersToSync");
            context.removeSharedData("newUsers");
            context.removeSharedData("newVimUsers");
            context.removeSharedData("agentUsers");
            context.removeSharedData("userIdMapping");
            context.removeSharedData("userRoles");
            context.removeSharedData("processedAgents");
            
            log.debug("清理步骤间临时数据完成");
        } catch (Exception e) {
            log.warn("清理步骤间临时数据失败", e);
            // 临时数据清理失败不影响主流程
        }
    }
    
    /**
     * 生成同步报告
     */
    private String generateSyncReport(BatchOperationResult<SysUser> syncResult, List<String> processedAgents) {
        StringBuilder report = new StringBuilder();
        report.append("用户同步执行报告:\n");
        report.append(String.format("- 总用户数: %d\n", syncResult.getTotalCount()));
        report.append(String.format("- 成功同步: %d\n", syncResult.getSuccessCount()));
        report.append(String.format("- 跳过用户: %d\n", syncResult.getSkipCount()));
        report.append(String.format("- 失败用户: %d\n", syncResult.getFailureCount()));
        
        if (processedAgents != null && !processedAgents.isEmpty()) {
            report.append(String.format("- 处理代理: %d\n", processedAgents.size()));
        }
        
        report.append("同步操作已完成。");
        
        return report.toString();
    }
    
    /**
     * 完成清理结果
     */
    public static class CompleteCleanupResult {
        private final BatchOperationResult<SysUser> syncResult;
        private final String syncReport;
        
        public CompleteCleanupResult(BatchOperationResult<SysUser> syncResult, String syncReport) {
            this.syncResult = syncResult;
            this.syncReport = syncReport;
        }
        
        // Getters
        public BatchOperationResult<SysUser> getSyncResult() { return syncResult; }
        public String getSyncReport() { return syncReport; }
    }
}
