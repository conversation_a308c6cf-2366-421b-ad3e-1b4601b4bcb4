/**
 * 主播个人信息详情页面
 * 
 * 为主播用户提供查看自己详细信息的独立页面
 * 整合了主播基本信息和统计数据
 */

import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { addDays, startOfDay, endOfDay } from 'date-fns'
import {
  User,
  Phone,
  Calendar,
  DollarSign,
  Shield,
  Key,
  Users,
  CreditCard,
  ShoppingCart,

  RefreshCw,
  TrendingUp,
  Target
} from 'lucide-react'
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/components/ui'
import { PagePermissionWrapper } from '@/components/auth/PermissionWrapper'
import { PermissionButton } from '@/components/auth/PermissionButton'
import { AuthService } from '@/services/auth'
import { OperationsService } from '@/services/operations'
import { formatTimestamp, formatCurrency } from '@/utils'
import { toast } from '@/hooks/useToast'
import { DateRangePicker } from '@/components/ui'
import { useAuthStore } from '@/stores/auth'
import { ROLES } from '@/constants'
import { useCurrentUserAnchorStats, useStatsFormatter } from './hooks/useCurrentUserAnchorStats'
import type { AnchorListResponse, AnchorQueryRequest } from '@/pages/yunying/types/operations'
import type { DateRange } from 'react-day-picker'

/**
 * 用户状态映射
 */
const USER_STATE_MAP = {
  1: { label: '正常', variant: 'success' as const },
  2: { label: '禁用', variant: 'destructive' as const }
}

/**
 * 身份类型映射
 */
const IDENTITY_MAP = {
  2: { label: '线上主播', variant: 'default' as const },
  3: { label: '线下主播', variant: 'secondary' as const }
}

/**
 * 认证状态映射
 */
const AUTH_STATUS_MAP = {
  0: { label: '未实名', variant: 'secondary' as const },
  1: { label: '已实名', variant: 'success' as const }
}

/**
 * 主播个人信息详情页面组件
 */
export default function AnchorProfile() {
  const navigate = useNavigate()
  const { roles } = useAuthStore()
  const [anchorInfo, setAnchorInfo] = useState<AnchorListResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 检查当前用户是否是主播角色
  const isAnchorUser = roles?.includes(ROLES.ANCHOR) || false

  // 使用新的统计数据hook
  const {
    statsData,
    firstRechargeData,
    statsLoading,
    firstRechargeLoading,
    error: statsError,
    loadStats,
    refreshAll,
    clearError
  } = useCurrentUserAnchorStats()

  // 统计数据格式化工具
  const { formatCurrency: formatCurrencyValue, formatPercentage, formatNumber } = useStatsFormatter()

  // 时间筛选状态（用于统计数据）
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => ({
    from: startOfDay(addDays(new Date(), -29)),
    to: endOfDay(new Date()),
  }))

  /**
   * 获取当前用户的主播信息
   */
  const fetchCurrentAnchorInfo = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // 先获取当前用户基本信息
      const userInfo = await AuthService.getCurrentUser()
      
      // 通过主播列表API获取详细的主播信息
      const anchorListParams: AnchorQueryRequest = {
        pageNum: 1,
        pageSize: 1,
        nickname: userInfo.username // 使用用户名搜索
      }
      
      const anchorListResult = await OperationsService.getAnchorList(anchorListParams)
      
      if (anchorListResult.records && anchorListResult.records.length > 0) {
        // 找到匹配的主播信息
        const matchedAnchor = anchorListResult.records.find(anchor => anchor.id === userInfo.id)
        if (matchedAnchor) {
          setAnchorInfo(matchedAnchor)
        } else {
          // 如果没有找到匹配的，使用第一个结果
          setAnchorInfo(anchorListResult.records[0])
        }
      } else {
        // 如果没有找到主播信息，创建一个基本的主播信息对象
        const basicAnchorInfo: AnchorListResponse = {
          id: userInfo.id,
          nickname: userInfo.nickname || userInfo.username,
          username: userInfo.username,
          phone: userInfo.phone || '',
          userimage: userInfo.avatar,
          state: userInfo.status || 1,
          identity: 2, // 默认为线上主播
          isauth: 0, // 默认未实名
          coin: 0, // 默认余额
          key: '0', // 默认钥匙数量
          subUserCount: 0, // 默认下级用户数量
          createTime: new Date(userInfo.createTime || Date.now()).getTime() / 1000,
          lastLoginTime: userInfo.lastLoginTime ? new Date(userInfo.lastLoginTime).getTime() / 1000 : undefined,
          lastLoginIp: '',
          inviteCode: '',
          level: 1,
          exp: 0,
          managerName: '',
          managerId: 0
        }
        setAnchorInfo(basicAnchorInfo)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取主播信息失败'
      setError(errorMessage)
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理查看下级用户
   */
  const handleViewSubUsers = (anchor: AnchorListResponse) => {
    navigate(`/yunying/sub-users?anchorId=${anchor.id}&anchorName=${encodeURIComponent(anchor.nickname)}`)
  }

  /**
   * 处理查看充值明细
   */
  const handleViewRechargeDetails = (anchor: AnchorListResponse) => {
    navigate(`/yunying/recharge-details?anchorId=${anchor.id}&anchorName=${encodeURIComponent(anchor.nickname)}&type=anchor`)
  }

  /**
   * 处理查看消费明细
   */
  const handleViewConsumeDetails = (anchor: AnchorListResponse) => {
    navigate(`/yunying/consume-details?anchorId=${anchor.id}&anchorName=${encodeURIComponent(anchor.nickname)}&type=anchor`)
  }



  /**
   * 刷新数据
   */
  const handleRefresh = () => {
    fetchCurrentAnchorInfo()
  }

  // 页面初始化时获取主播信息
  useEffect(() => {
    fetchCurrentAnchorInfo()
  }, [])

  // 当主播信息加载完成后，加载统计数据
  useEffect(() => {
    if (anchorInfo && dateRange?.from && dateRange?.to) {
      const startTime = Math.floor(dateRange.from.getTime() / 1000)
      const endTime = Math.floor(dateRange.to.getTime() / 1000)

      // 根据用户角色传递不同的参数
      if (isAnchorUser) {
        // 主播用户：不需要传递anchorId，后端会自动使用当前登录用户的ID
        refreshAll(undefined, startTime, endTime)
      } else {
        // 管理员用户：需要传递vim_user ID
        refreshAll(anchorInfo.id, startTime, endTime)
      }
    }
  }, [anchorInfo, dateRange, isAnchorUser, refreshAll])

  // 处理时间范围变化
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range)
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">加载中...</span>
        </div>
      </div>
    )
  }

  if (error || !anchorInfo) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">
          <div className="text-red-500 mb-4">{error || '获取主播信息失败'}</div>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
        </div>
      </div>
    )
  }

  return (
    <PagePermissionWrapper permissions={['operations:anchors:view']}>
      <div className="container mx-auto p-6 space-y-6">
        {/* 页面头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/*<Button variant="ghost" size="sm" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>*/}
            <div>
              <h1 className="text-2xl font-bold">我的信息</h1>
              <p className="text-muted-foreground">查看和管理您的个人信息及统计数据</p>
            </div>
          </div>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>

        {/* 主播基本信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start space-x-6">
              {/* 头像 */}
              <Avatar className="h-16 w-16">
                <AvatarImage src={anchorInfo.userimage} alt={anchorInfo.nickname} />
                <AvatarFallback className="text-lg">
                  {anchorInfo.nickname.charAt(0)}
                </AvatarFallback>
              </Avatar>

              {/* 基本信息 */}
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">用户ID</label>
                  <p className="text-sm font-mono">{anchorInfo.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">昵称</label>
                  <p className="text-sm">{anchorInfo.nickname}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">用户名</label>
                  <p className="text-sm">{anchorInfo.username}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">手机号</label>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">{anchorInfo.phone}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">状态</label>
                  <div>
                    <Badge variant={USER_STATE_MAP[anchorInfo.state as keyof typeof USER_STATE_MAP]?.variant}>
                      {USER_STATE_MAP[anchorInfo.state as keyof typeof USER_STATE_MAP]?.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">身份类型</label>
                  <div>
                    <Badge variant={IDENTITY_MAP[anchorInfo.identity as keyof typeof IDENTITY_MAP]?.variant}>
                      {IDENTITY_MAP[anchorInfo.identity as keyof typeof IDENTITY_MAP]?.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">实名状态</label>
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 mr-1 text-muted-foreground" />
                    <Badge variant={AUTH_STATUS_MAP[anchorInfo.isauth as keyof typeof AUTH_STATUS_MAP]?.variant}>
                      {AUTH_STATUS_MAP[anchorInfo.isauth as keyof typeof AUTH_STATUS_MAP]?.label}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">注册时间</label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">{formatTimestamp(anchorInfo.createTime)}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">最后登录</label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="text-sm">
                      {anchorInfo.lastLoginTime ? formatTimestamp(anchorInfo.lastLoginTime) : '从未登录'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 资产信息卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">余额</p>
                  <p className="text-2xl font-bold">{formatCurrency(anchorInfo.coin)}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">钥匙</p>
                  <p className="text-2xl font-bold">{anchorInfo.key}</p>
                </div>
                <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Key className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">下级用户</p>
                  <p className="text-2xl font-bold">{anchorInfo.subUserCount}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 统计数据区域 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                主播统计
              </CardTitle>
              <div className="flex items-center space-x-2">
                <DateRangePicker
                  onDateChange={handleDateRangeChange}
                  className="w-[300px]"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (anchorInfo && dateRange?.from && dateRange?.to) {
                      const startTime = Math.floor(dateRange.from.getTime() / 1000)
                      const endTime = Math.floor(dateRange.to.getTime() / 1000)
                      if (isAnchorUser) {
                        refreshAll(undefined, startTime, endTime)
                      } else {
                        refreshAll(anchorInfo.id, startTime, endTime)
                      }
                    }
                  }}
                  disabled={statsLoading || firstRechargeLoading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${(statsLoading || firstRechargeLoading) ? 'animate-spin' : ''}`} />
                  刷新
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* 基础统计数据 */}
            {statsLoading ? (
              <div className="flex items-center justify-center h-32">
                <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                <span className="ml-2 text-muted-foreground">加载统计数据中...</span>
              </div>
            ) : statsError ? (
              <div className="text-center py-8">
                <div className="text-red-500 mb-4">服务器错误，请稍后重试</div>
                <Button
                  onClick={() => {
                    clearError()
                    if (anchorInfo && dateRange?.from && dateRange?.to) {
                      const startTime = Math.floor(dateRange.from.getTime() / 1000)
                      const endTime = Math.floor(dateRange.to.getTime() / 1000)
                      if (isAnchorUser) {
                        loadStats(undefined, startTime, endTime)
                      } else {
                        loadStats(anchorInfo.id, startTime, endTime)
                      }
                    }
                  }}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重试
                </Button>
              </div>
            ) : statsData ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">总充值</p>
                        <p className="text-xl font-bold">{formatCurrencyValue(statsData.totalRecharge || 0)}</p>
                      </div>
                      <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                        <DollarSign className="h-5 w-5 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">总消费</p>
                        <p className="text-xl font-bold">{formatCurrencyValue(statsData.totalConsume || 0)}</p>
                      </div>
                      <div className="h-10 w-10 bg-red-100 rounded-full flex items-center justify-center">
                        <ShoppingCart className="h-5 w-5 text-red-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">用户总数</p>
                        <p className="text-xl font-bold">{formatNumber(statsData.userCount || 0)}</p>
                      </div>
                      <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">新增用户</p>
                        <p className="text-xl font-bold">{formatNumber(statsData.periodNewUserCount || 0)}</p>
                      </div>
                      <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <Target className="h-5 w-5 text-purple-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : null}

            {/* 首充统计数据 */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Target className="h-5 w-5 mr-2" />
                首充统计
              </h3>
              {firstRechargeLoading ? (
                <div className="flex items-center justify-center h-24">
                  <RefreshCw className="h-5 w-5 animate-spin text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">加载首充数据中...</span>
                </div>
              ) : firstRechargeData ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">首充用户数</p>
                        <p className="text-2xl font-bold text-blue-600">{formatNumber(firstRechargeData.firstRechargeUserCount || 0)}</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">首充转化率</p>
                        <p className="text-2xl font-bold text-green-600">{formatPercentage(firstRechargeData.firstRechargeConversionRate || 0)}</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">首充总金额</p>
                        <p className="text-2xl font-bold text-orange-600">{formatCurrencyValue(firstRechargeData.totalFirstRechargeAmount || 0)}</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">平均首充金额</p>
                        <p className="text-2xl font-bold text-purple-600">{formatCurrencyValue(firstRechargeData.avgFirstRechargeAmount || 0)}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  暂无首充统计数据
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 操作区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              管理操作
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 下级用户列表按钮 - 需要用户查看权限 */}
              <PermissionButton
                permissions={['operations:user:list']}
                variant="outline"
                className="h-20 flex-col space-y-2"
                onClick={() => handleViewSubUsers(anchorInfo)}
                title="查看下级用户列表"
              >
                <Users className="h-8 w-8 text-blue-600" />
                <div className="text-center">
                  <div className="font-medium">下级用户列表</div>
                  <div className="text-xs text-muted-foreground">
                    查看下级用户列表
                  </div>
                </div>
              </PermissionButton>

              {/* 充值明细按钮 - 需要充值查看权限 */}
              <PermissionButton
                permissions={['operations:user:recharge']}
                variant="outline"
                className="h-20 flex-col space-y-2"
                onClick={() => handleViewRechargeDetails(anchorInfo)}
                title="查看充值明细"
              >
                <CreditCard className="h-8 w-8 text-green-600" />
                <div className="text-center">
                  <div className="font-medium">充值明细</div>
                  <div className="text-xs text-muted-foreground">
                    查看充值记录
                  </div>
                </div>
              </PermissionButton>

              {/* 消费明细按钮 - 需要消费查看权限 */}
              <PermissionButton
                permissions={['operations:user:consume']}
                variant="outline"
                className="h-20 flex-col space-y-2"
                onClick={() => handleViewConsumeDetails(anchorInfo)}
                title="查看消费明细"
              >
                <ShoppingCart className="h-8 w-8 text-orange-600" />
                <div className="text-center">
                  <div className="font-medium">消费明细</div>
                  <div className="text-xs text-muted-foreground">
                    查看消费记录
                  </div>
                </div>
              </PermissionButton>
            </div>
          </CardContent>
        </Card>
      </div>
    </PagePermissionWrapper>
  )
}
