/**
 * 用户充值明细页面
 * 
 * 展示用户的充值记录，支持搜索、筛选、排序和分页功能
 */

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import {
  Search,
  Filter,
  Download,
  RefreshCw,
  CreditCard,
  Calendar,
  DollarSign,
  Coins,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowUpDown,
  ArrowLeft
} from 'lucide-react'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  Skeleton,
  Alert,
  AlertDescription
} from '@/components/ui'
import { OperationsService } from '@/services/operations'
import type { RechargeQueryRequest, RechargeDetailResponse, PageResult } from '@/services/operations'
import { formatTimestamp, formatCurrency } from '@/utils'
import { toast } from '@/hooks/useToast'

/**
 * 充值状态映射
 */
const RECHARGE_STATE_MAP = {
  1: { 
    label: '未支付', 
    variant: 'secondary' as const, 
    icon: AlertCircle,
    color: 'text-yellow-600'
  },
  2: { 
    label: '已支付', 
    variant: 'default' as const,
    icon: CheckCircle,
    color: 'text-green-600'
  },
  3: { 
    label: '支付异常', 
    variant: 'destructive' as const, 
    icon: XCircle,
    color: 'text-red-600'
  }
}

/**
 * 支付方式颜色映射
 */
const PAYMENT_METHOD_COLORS = {
  '微信支付': 'text-green-600',
  '支付宝': 'text-blue-600',
  '其他': 'text-gray-600'
}

/**
 * 用户充值明细页面组件
 */
export default function RechargeDetails() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const userId = Number(searchParams.get('userId'))
  const anchorId = Number(searchParams.get('anchorId'))
  const userName = searchParams.get('userName') || '未知用户'
  const anchorName = searchParams.get('anchorName') || '未知主播'
  const queryType = searchParams.get('type') || 'user' // 'user' 或 'anchor'

  // 根据查询类型确定显示的ID和名称
  const displayId = queryType === 'anchor' ? anchorId : userId
  const displayName = queryType === 'anchor' ? anchorName : userName
  const displayTitle = queryType === 'anchor' ? '主播充值明细' : '用户充值明细'

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<PageResult<RechargeDetailResponse> | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 查询参数状态
  const [queryParams, setQueryParams] = useState<RechargeQueryRequest>({
    pageNum: 1,
    pageSize: 20,
    orderBy: 'createTime',
    orderDirection: 'DESC'
  })

  // 搜索表单状态
  const [searchForm, setSearchForm] = useState({
    orderId: '',
    payId: '',
    minAmount: '',
    maxAmount: '',
    state: 'all',
    isFirstRecharge: 'all'
  })

  /**
   * 获取充值明细列表
   */
  const fetchRechargeDetails = useCallback(async (params: RechargeQueryRequest) => {
    if (!displayId) {
      setError(`缺少${queryType === 'anchor' ? '主播' : '用户'}ID参数`)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await OperationsService.getRechargeDetails(displayId, params)
      setData(result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取充值明细失败'
      setError(errorMessage)
      toast({
        title: '获取数据失败',
        description: errorMessage,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [userId])

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    const newParams: RechargeQueryRequest = {
      ...queryParams,
      pageNum: 1,
      ...Object.fromEntries(
        Object.entries(searchForm).filter(([_, value]) => value !== '' && value !== 'all')
      ),
      minAmount: searchForm.minAmount ? Number(searchForm.minAmount) : undefined,
      maxAmount: searchForm.maxAmount ? Number(searchForm.maxAmount) : undefined
    }
    setQueryParams(newParams)
    fetchRechargeDetails(newParams)
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    const resetForm = {
      orderId: '',
      payId: '',
      minAmount: '',
      maxAmount: '',
      state: 'all',
      isFirstRecharge: 'all'
    }
    setSearchForm(resetForm)

    const resetParams: RechargeQueryRequest = {
      pageNum: 1,
      pageSize: 20,
      orderBy: 'createTime',
      orderDirection: 'DESC'
    }
    setQueryParams(resetParams)
    fetchRechargeDetails(resetParams)
  }

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number) => {
    const newParams = { ...queryParams, pageNum: page }
    setQueryParams(newParams)
    fetchRechargeDetails(newParams)
  }

  /**
   * 处理每页条数变化
   */
  const handlePageSizeChange = (pageSize: number) => {
    const newParams = { ...queryParams, pageNum: 1, pageSize }
    setQueryParams(newParams)
    fetchRechargeDetails(newParams)
  }

  /**
   * 处理排序变化
   */
  const handleSortChange = (field: string) => {
    const newDirection = queryParams.orderBy === field && queryParams.orderDirection === 'DESC' 
      ? 'ASC' 
      : 'DESC'
    
    const newParams = {
      ...queryParams,
      orderBy: field,
      orderDirection: newDirection as 'ASC' | 'DESC'
    }
    setQueryParams(newParams)
    fetchRechargeDetails(newParams)
  }

  /**
   * 处理导出
   */
  const handleExport = () => {
    toast({
      title: '导出功能',
      description: '导出功能正在开发中...',
      variant: 'default'
    })
  }

  /**
   * 返回主播详情页面（仅当查询类型为主播时显示）
   */
  const handleBackToAnchorProfile = () => {
    navigate('/yunying/profile')
  }

  // 初始化加载数据
  useEffect(() => {
    fetchRechargeDetails(queryParams)
  }, [])

  // 渲染加载状态
  if (loading && !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>充值明细</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {queryType === 'anchor' && (
            <Button variant="ghost" size="sm" onClick={handleBackToAnchorProfile}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回主播详情
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold">{displayTitle}</h1>
            <p className="text-muted-foreground">
              {queryType === 'anchor' ? '主播' : '用户'}：{displayName}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchRechargeDetails(queryParams)}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 搜索筛选区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            搜索筛选
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">订单ID</label>
              <Input
                placeholder="请输入订单ID"
                value={searchForm.orderId}
                onChange={(e) => setSearchForm(prev => ({ ...prev, orderId: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">支付ID</label>
              <Input
                placeholder="请输入支付ID"
                value={searchForm.payId}
                onChange={(e) => setSearchForm(prev => ({ ...prev, payId: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">最小金额</label>
              <Input
                type="number"
                placeholder="请输入最小金额"
                value={searchForm.minAmount}
                onChange={(e) => setSearchForm(prev => ({ ...prev, minAmount: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">最大金额</label>
              <Input
                type="number"
                placeholder="请输入最大金额"
                value={searchForm.maxAmount}
                onChange={(e) => setSearchForm(prev => ({ ...prev, maxAmount: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">支付状态</label>
              <Select
                value={searchForm.state}
                onValueChange={(value) => setSearchForm(prev => ({ ...prev, state: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择支付状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="1">未支付</SelectItem>
                  <SelectItem value="2">已支付</SelectItem>
                  <SelectItem value="3">支付异常</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">首充状态</label>
              <Select
                value={searchForm.isFirstRecharge}
                onValueChange={(value) => setSearchForm(prev => ({ ...prev, isFirstRecharge: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择首充状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="true">首充</SelectItem>
                  <SelectItem value="false">非首充</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
            <Button variant="outline" onClick={handleReset}>
              重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 数据表格 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>充值记录</CardTitle>
            <div className="text-sm text-muted-foreground">
              共 {data?.total || 0} 条记录
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>订单ID</TableHead>
                  <TableHead>支付ID</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('amount')}
                  >
                    <div className="flex items-center">
                      充值金额
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead>获得电能</TableHead>
                  <TableHead>支付状态</TableHead>
                  <TableHead>支付方式</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('createTime')}
                  >
                    <div className="flex items-center">
                      创建时间
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSortChange('updateTime')}
                  >
                    <div className="flex items-center">
                      更新时间
                      <ArrowUpDown className="h-4 w-4 ml-1" />
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                    </TableRow>
                  ))
                ) : data?.records?.length ? (
                  data.records.map((record) => {
                    const stateInfo = RECHARGE_STATE_MAP[record.state as keyof typeof RECHARGE_STATE_MAP]
                    const StateIcon = stateInfo?.icon || AlertCircle
                    
                    return (
                      <TableRow key={record.id}>
                        <TableCell className="font-medium font-mono text-sm">
                          {record.id}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {record.payid}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                            <span className="font-medium">
                              {formatCurrency(record.amount)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Coins className="h-4 w-4 mr-1 text-yellow-600" />
                            <span className="font-medium">
                              {record.coin.toLocaleString()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <StateIcon className={`h-4 w-4 mr-2 ${stateInfo?.color}`} />
                            <Badge variant={stateInfo?.variant}>
                              {stateInfo?.label}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className={PAYMENT_METHOD_COLORS[record.paymentMethod as keyof typeof PAYMENT_METHOD_COLORS] || 'text-gray-600'}>
                              {record.paymentMethod}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="text-sm">
                              {formatTimestamp(record.createTime)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="text-sm">
                              {formatTimestamp(record.updateTime)}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                      暂无数据
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页组件 */}
          {data && data.total > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground w-14">每页显示</span>
                <Select
                  value={queryParams.pageSize.toString()}
                  onValueChange={(value) => handlePageSizeChange(Number(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">条</span>
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                        onClick={() => handlePageChange(queryParams.pageNum - 1)}
                        className={queryParams.pageNum <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        size={undefined}                    />
                  </PaginationItem>
                  
                  {Array.from({ length: Math.min(5, data.totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => handlePageChange(page)}
                          isActive={page === queryParams.pageNum}
                          className="cursor-pointer"
                          size={undefined}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}
                  
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => handlePageChange(queryParams.pageNum + 1)}
                      className={queryParams.pageNum >= data.totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      size={undefined}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
